using STranslate.Model;
using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace STranslate.Style.Converters
{
    public class RoleToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is RoleEnum role)
            {
                return role == RoleEnum.User ? new SolidColorBrush(Colors.DodgerBlue) : new SolidColorBrush(Colors.LightGray);
            }
            return new SolidColorBrush(Colors.LightGray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
