using CommunityToolkit.Mvvm.ComponentModel;
using Newtonsoft.Json;
using System.ComponentModel;

namespace STranslate.Model
{
    public partial class UserDefinePrompt : ObservableObject, ICloneable
    {
        [ObservableProperty]
        private string _name = "";

        [ObservableProperty]
        private bool _enabled;

        [ObservableProperty]
        private BindingList<Prompt> _prompts = [];

        public UserDefinePrompt(string name, BindingList<Prompt> prompts, bool enabled = false)
        {
            Name = name;
            Prompts = prompts;
            Enabled = enabled;
        }

        [JsonConstructor]
        public UserDefinePrompt(string name, bool enabled, BindingList<Prompt> prompts)
        {
            Name = name;
            Enabled = enabled;
            Prompts = prompts;
        }

        public object Clone()
        {
            var newPrompts = new BindingList<Prompt>();
            foreach (var prompt in Prompts)
            {
                newPrompts.Add((Prompt)prompt.Clone());
            }
            return new UserDefinePrompt(Name, newPrompts, Enabled);
        }
    }
}
