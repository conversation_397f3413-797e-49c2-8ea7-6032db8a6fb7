﻿using System.ComponentModel;
using System.Net.Http;
using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using STranslate.Helper;
using STranslate.Log;
using STranslate.Model;
using STranslate.Util;
using System.Text;
using System.Runtime.CompilerServices;

namespace STranslate.ViewModels.Preference.Translator;

public partial class TranslatorGemini : TranslatorLLMBase, ITranslatorLLM
{
    #region Constructor

    public TranslatorGemini()
        : this(Guid.NewGuid(), "https://generativelanguage.googleapis.com", "Gemini")
    {
    }

    public TranslatorGemini(
        Guid guid,
        string url,
        string name = "",
        IconType icon = IconType.Gemini,
        string appID = "",
        string appKey = "",
        bool isEnabled = true,
        ServiceType type = ServiceType.GeminiService
    )
    {
        Identify = guid;
        Url = url;
        Name = name;
        Icon = icon;
        AppID = appID;
        AppKey = appKey;
        IsEnabled = isEnabled;
        Type = type;
    }

    #endregion Constructor

    #region Properties

    [JsonIgnore]
    private BindingList<string> _models =
    [
        "gemini-2.0-flash",
        "gemini-2.5-flash-preview-04-17",
        "gemini-2.5-pro-preview-05-06",
    ];
    public override BindingList<string> Models
    {
        get => _models;
        set => SetProperty(ref _models, value);
    }

    [JsonIgnore]
    private BindingList<UserDefinePrompt> _userDefinePrompts =
    [
        new UserDefinePrompt(
            "翻译",
            [
                new Prompt(
                    "user",
                    "You are a professional translation engine, please translate the text into a colloquial, professional, elegant and fluent content, without the style of machine translation. You must only translate the text content, never interpret it."
                ),
                new Prompt("model", "Ok, I will only translate the text content, never interpret it"),
                new Prompt("user", "Translate the following text from en to zh: hello world"),
                new Prompt("model", "你好，世界"),
                new Prompt("user", "Translate the following text from $source to $target: $content")
            ],
            true
        ),
        new UserDefinePrompt(
            "润色",
            [
                new Prompt("user", "You are a text embellisher, you can only embellish the text, never interpret it."),
                new Prompt("model", "Ok, I will only embellish the text, never interpret it."),
                new Prompt("user", "Embellish the following text in $source: $content")
            ]
        ),
        new UserDefinePrompt(
            "总结",
            [
                new Prompt("user", "You are a text summarizer, you can only summarize the text, never interpret it."),
                new Prompt("model", "Ok, I will only summarize the text, never interpret it."),
                new Prompt("user", "Summarize the following text in $source: $content")
            ]
        )
    ];
    public override BindingList<UserDefinePrompt> UserDefinePrompts
    {
        get => _userDefinePrompts;
        set => SetProperty(ref _userDefinePrompts, value);
    }

    #endregion Properties

    #region Translator Test

    [property: JsonIgnore]
    [RelayCommand(IncludeCancelCommand = true)]
    private async Task TestAsync(CancellationToken token)
    {
        var result = "";
        var isCancel = false;
        try
        {
            IsTesting = true;
            var reqModel = new RequestModel("你好", LangEnum.zh_cn, LangEnum.en);
            await foreach (var item in TranslateAsync(reqModel, token))
            {
                result += item;
            }
        }
        catch (OperationCanceledException)
        {
            isCancel = true;
        }
        catch (Exception)
        {
            result = AppLanguageManager.GetString("Toast.VerifyFailed");
        }
        finally
        {
            IsTesting = false;
            if (!isCancel)
                ToastHelper.Show(result, WindowType.Preference);
        }
    }

    #endregion Translator Test

    #region Interface Implementation

    public new async IAsyncEnumerable<string> TranslateAsync(object request, [EnumeratorCancellation] CancellationToken token)
    {
        if (string.IsNullOrEmpty(Url) || string.IsNullOrEmpty(AppKey))
            throw new Exception("请先完善配置");

        if (request is not RequestModel req)
            throw new Exception($"请求数据出错: {request}");

        //检查语种
        var source = LangConverter(req.SourceLang) ?? throw new Exception($"该服务不支持{req.SourceLang.GetDescription()}");
        var target = LangConverter(req.TargetLang) ?? throw new Exception($"该服务不支持{req.TargetLang.GetDescription()}");
        var content = req.Text;

        UriBuilder uriBuilder = new(Url);

        // 选择模型
        var a_model = Model.Trim();
        a_model = string.IsNullOrEmpty(a_model) ? "gemini-2.0-flash" : a_model;

        if (uriBuilder.Path == "/")
            uriBuilder.Path = $"/v1beta/models/{a_model}:streamGenerateContent";

        // 加上 alt=sse 才是每个流传输结果为完整json，方便解析判断是否为最后一条
        uriBuilder.Query = $"alt=sse&key={AppKey}";

        // 替换Prompt关键字
        var a_messages =
            (UserDefinePrompts.FirstOrDefault(x => x.Enabled)?.Prompts ?? throw new Exception("请先完善Propmpt配置")).Clone();
        a_messages.ToList().ForEach(item =>
            item.Content = item.Content.Replace("$source", source).Replace("$target", target)
                .Replace("$content", content));

        // 温度限定
        var a_temperature = Math.Clamp(Temperature, 0, 2);
        
        // 构建请求数据
        var reqData = new
        {
            contents = a_messages.Select(e => new { role = e.Role, parts = new[] { new { text = e.Content } } }),
            generationConfig = new { temperature = a_temperature },
            safetySettings = new[]
            {
                new { category = "HARM_CATEGORY_HARASSMENT", threshold = "BLOCK_NONE"},         //骚扰内容。
                new { category = "HARM_CATEGORY_HATE_SPEECH", threshold = "BLOCK_NONE"},        //仇恨言论和内容。
                new { category = "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold = "BLOCK_NONE"},  //露骨色情内容。
                new { category = "HARM_CATEGORY_DANGEROUS_CONTENT", threshold = "BLOCK_NONE"},  //危险内容。
            }
        };

        // 为了流式输出与MVVM还是放这里吧
        var jsonData = JsonConvert.SerializeObject(reqData);

        try
        {
            var resp = HttpUtil.PostAsync(uriBuilder.Uri, jsonData, null, token);
            await foreach (var msg in resp)
            {
                if (string.IsNullOrEmpty(msg?.Trim()))
                    continue;

                var preprocessString = msg.Replace("data:", "").Trim();

                // 解析JSON数据
                var parsedData = JsonConvert.DeserializeObject<JObject>(preprocessString);

                if (parsedData is null)
                    continue;

                // 提取content的值
                var contentValue = parsedData["candidates"]?.FirstOrDefault()?["content"]?["parts"]?.FirstOrDefault()?["text"]?.ToString();

                if (string.IsNullOrEmpty(contentValue))
                    continue;

                // 结束时处理最后多余的\n
                if ((parsedData["candidates"]?.FirstOrDefault()?["finishReason"]?.ToString() ?? "") == "STOP" && contentValue.EndsWith('\n'))
                    contentValue = contentValue.TrimEnd('\n');

                yield return contentValue;
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (HttpRequestException ex) when (ex.StatusCode == null)
        {
            var msg = $"请检查服务是否可以正常访问: {Name} ({Url}).";
            throw new HttpRequestException(msg);
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException is { } innEx)
            {
                var innMsg = JsonConvert.DeserializeObject<JObject>(innEx.Message);
                msg += $