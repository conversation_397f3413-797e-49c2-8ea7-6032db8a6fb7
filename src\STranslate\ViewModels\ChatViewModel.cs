using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using STranslate.Model;
using STranslate.ViewModels.Preference;
using System; 
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace STranslate.ViewModels
{
    public partial class ChatViewModel : ObservableObject
    {
        [ObservableProperty]
        private ObservableCollection<Prompt> _chatHistory = [];

        [ObservableProperty]
        private string _userInput = "";

        [ObservableProperty]
        private ITranslatorLLM? _selectedTranslator;

        [ObservableProperty]
        private ObservableCollection<ITranslatorLLM> _llmServices = [];

        public ChatViewModel()
        {
            // Load available LLM services
            var llmServices = Singleton<TranslatorViewModel>.Instance.CurTransServiceList.OfType<ITranslatorLLM>();
            LlmServices = new ObservableCollection<ITranslatorLLM>(llmServices);
            SelectedTranslator = LlmServices.FirstOrDefault();
        }

        [RelayCommand]
        private async Task SendMessageAsync()
        {
            if (string.IsNullOrWhiteSpace(UserInput) || SelectedTranslator is null || SelectedTranslator.IsExecuting)
            {
                return;
            }

            // Add user message to chat
            ChatHistory.Add(new Prompt { Role = RoleEnum.User, Content = UserInput });
            var currentInput = UserInput;
            UserInput = ""; // Clear input box

            try
            {
                // Prepare the history for the API call
                var history = new List<Prompt>(ChatHistory);

                var result = await SelectedTranslator.TranslateAsync(history, default);

                if (result != null && !string.IsNullOrEmpty(result.Result?.ToString()))
                {
                    // Add assistant response to chat
                    ChatHistory.Add(new Prompt { Role = RoleEnum.Assistant, Content = result.Result.ToString() ?? "" });
                }
                else
                {
                    // Handle error or empty response
                    ChatHistory.Add(new Prompt { Role = RoleEnum.Assistant, Content = "Sorry, I could not get a response." });
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                ChatHistory.Add(new Prompt { Role = RoleEnum.Assistant, Content = $"An error occurred: {ex.Message}" });
            }
            finally
            {
                // Maybe scroll to the latest message
            }
        }
    }
}
