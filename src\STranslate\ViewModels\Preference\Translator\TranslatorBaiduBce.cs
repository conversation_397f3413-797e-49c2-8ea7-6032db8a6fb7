﻿using System.ComponentModel;
using System.Net.Http;
using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using STranslate.Helper;
using STranslate.Log;
using STranslate.Model;
using STranslate.Util;
using System.Text;
using System.Runtime.CompilerServices;

namespace STranslate.ViewModels.Preference.Translator;

public partial class TranslatorBaiduBce : TranslatorLLMBase, ITranslatorLLM
{
    #region Constructor

    public TranslatorBaiduBce()
        : this(Guid.NewGuid(), "https://aip.baidubce.com/rpc/2.0/ai_custom/v1", "百度千帆")
    {
    }

    public TranslatorBaiduBce(
        Guid guid,
        string url,
        string name = "",
        IconType icon = IconType.BaiduBce,
        string appID = "",
        string appKey = "",
        bool isEnabled = true,
        ServiceType type = ServiceType.BaiduBceService
    )
    {
        Identify = guid;
        Url = url;
        Name = name;
        Icon = icon;
        AppID = appID;
        AppKey = appKey;
        IsEnabled = isEnabled;
        Type = type;
    }

    #endregion Constructor

    #region Properties

    [JsonIgnore]
    private BindingList<string> _models =
    [
        "ernie_speed",
        "deepseek-r1",
    ];
    public override BindingList<string> Models
    {
        get => _models;
        set => SetProperty(ref _models, value);
    }

    #endregion

    #region Translator Test

    [property: JsonIgnore]
    [RelayCommand(IncludeCancelCommand = true)]
    private async Task TestAsync(CancellationToken token)
    {
        var result = "";
        var isCancel = false;
        try
        {
            IsTesting = true;
            var reqModel = new RequestModel("你好", LangEnum.zh_cn, LangEnum.en);
            await foreach (var item in TranslateAsync(reqModel, token))
            {
                result += item;
            }
        }
        catch (OperationCanceledException)
        {
            isCancel = true;
        }
        catch (Exception)
        {
            result = AppLanguageManager.GetString("Toast.VerifyFailed");
        }
        finally
        {
            IsTesting = false;
            if (!isCancel)
                ToastHelper.Show(result, WindowType.Preference);
        }
    }

    #endregion Translator Test

    #region Interface Implementation

    public new async IAsyncEnumerable<string> TranslateAsync(object request, [EnumeratorCancellation] CancellationToken token)
    {
        if (string.IsNullOrEmpty(Url) || string.IsNullOrEmpty(AppKey))
            throw new Exception("请先完善配置");

        if (request is not RequestModel req)
            throw new Exception($"请求数据出错: {request}");

        //检查语种
        var source = LangConverter(req.SourceLang) ?? throw new Exception($"该服务不支持{req.SourceLang.GetDescription()}");
        var target = LangConverter(req.TargetLang) ?? throw new Exception($"该服务不支持{req.TargetLang.GetDescription()}");
        var content = req.Text;

        // 选择模型
        var a_model = Model.Trim();
        a_model = string.IsNullOrEmpty(a_model) ? "ernie_speed" : a_model;

        // 替换Prompt关键字
        var a_messages =
            (UserDefinePrompts.FirstOrDefault(x => x.Enabled)?.Prompts ?? throw new Exception("请先完善Propmpt配置")).Clone();
        a_messages.ToList().ForEach(item =>
            item.Content = item.Content.Replace("$source", source).Replace("$target", target)
                .Replace("$content", content));

        // 温度限定
        var a_temperature = Math.Clamp(Temperature, 0, 2);

        #region 获取accesstoken

        var accessToken = string.Empty;
        try
        {
            var accessTokenUrl = "https://aip.baidubce.com/oauth/2.0/token";
            var formData = new Dictionary<string, string>
            {
                { "grant_type", "client_credentials" },
                { "client_id", AppID },
                { "client_secret", AppKey }
            };
            var resp = await HttpUtil.PostAsync(accessTokenUrl, formData, token);
            accessToken = JObject.Parse(resp)?["access_token"]?.ToString() ??
                          throw new Exception("get accesstoken is null");
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException is Exception innEx)
            {
                var innMsg = JsonConvert.DeserializeObject<JObject>(innEx.Message);
                msg += $