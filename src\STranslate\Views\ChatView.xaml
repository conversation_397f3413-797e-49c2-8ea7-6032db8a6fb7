<UserControl x:Class="STranslate.Views.ChatView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:STranslate.Views"
             xmlns:vm="clr-namespace:STranslate.ViewModels"
             xmlns:model="clr-namespace:STranslate.Model"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.DataContext>
        <vm:ChatViewModel/>
    </UserControl.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <ComboBox Grid.Row="0" 
                  ItemsSource="{Binding LlmServices}"
                  SelectedItem="{Binding SelectedTranslator}"
                  DisplayMemberPath="Name"
                  Margin="5"/>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding ChatHistory}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate DataType="{x:Type model:Prompt}">
                        <Grid Margin="5">
                            <Border CornerRadius="10" Padding="10"
                                    HorizontalAlignment="{Binding Role, Converter={StaticResource RoleToAlignmentConverter}}"
                                    Background="{Binding Role, Converter={StaticResource RoleToColorConverter}}">
                                <TextBlock Text="{Binding Content}" TextWrapping="Wrap" Foreground="White"/>
                            </Border>
                        </Grid>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBox Grid.Column="0" Text="{Binding UserInput, UpdateSourceTrigger=PropertyChanged}" Margin="5" VerticalContentAlignment="Center"/>
            <Button Grid.Column="1" Content="Send" Command="{Binding SendMessageCommand}" Margin="5"/>
        </Grid>
    </Grid>
</UserControl>
