using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace STranslate.Model
{
    public interface IOCRLLM : IOCR
    {
        /// <summary>
        /// LLM
        /// </summary>
        /// <param name="prompts"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        Task<OcrResult> ExecuteAsync(IEnumerable<Prompt> prompts, CancellationToken token);
    }
}