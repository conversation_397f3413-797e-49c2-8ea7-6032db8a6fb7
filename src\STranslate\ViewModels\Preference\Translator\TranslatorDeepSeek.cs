﻿using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using STranslate.Helper;
using STranslate.Log;
using STranslate.Model;
using STranslate.Util;
using System.ComponentModel;
using System.Net.Http;
using System.Text;
using System.Runtime.CompilerServices;

namespace STranslate.ViewModels.Preference.Translator;

public partial class TranslatorDeepSeek : TranslatorLLMBase, ITranslatorLLM
{
    #region Constructor
    public TranslatorDeepSeek()
        : this(Guid.NewGuid(), "https://api.deepseek.com", "DeepSeek")
    {
    }
    public TranslatorDeepSeek(
        Guid guid,
        string url,
        string name = "",
        IconType icon = IconType.DeepSeek,
        string appID = "",
        string appKey = "",
        bool isEnabled = true,
        ServiceType type = ServiceType.DeepSeekService
)
    {
        Identify = guid;
        Url = url;
        Name = name;
        Icon = icon;
        AppID = appID;
        AppKey = appKey;
        IsEnabled = isEnabled;
        Type = type;
    }

    #endregion Constructor

    #region Properties

    [JsonIgnore] private string _model = "deepseek-chat";
    public override string Model
    {
        get => _model;
        set => SetProperty(ref _model, value);
    }

    [JsonIgnore]
    private BindingList<string> _models =
    [
        "deepseek-chat",
        "deepseek-reasoner",
    ];
    public override BindingList<string> Models
    {
        get => _models;
        set => SetProperty(ref _models, value);
    }

    #endregion

    #region Translator Test

    [property: JsonIgnore]
    [RelayCommand(IncludeCancelCommand = true)]
    private async Task TestAsync(CancellationToken token)
    {
        var result = "";
        var isCancel = false;
        try
        {
            IsTesting = true;
            var reqModel = new RequestModel("你好", LangEnum.zh_cn, LangEnum.en);
            await foreach (var item in TranslateAsync(reqModel, token))
            {
                result += item;
            }
        }
        catch (OperationCanceledException)
        {
            isCancel = true;
        }
        catch (Exception)
        {
            result = AppLanguageManager.GetString("Toast.VerifyFailed");
        }
        finally
        {
            IsTesting = false;
            if (!isCancel)
                ToastHelper.Show(result, WindowType.Preference);
        }
    }

    #endregion Translator Test

    #region Interface Implementation

    public new async IAsyncEnumerable<string> TranslateAsync(object request, [EnumeratorCancellation] CancellationToken token)
    {
        if (string.IsNullOrEmpty(Url) /* || string.IsNullOrEmpty(AppKey)*/)
            throw new Exception("请先完善配置");

        if (request is not RequestModel req)
            throw new Exception($"请求数据出错: {request}");

        //检查语种
        var source = LangConverter(req.SourceLang) ?? throw new Exception($"该服务不支持{req.SourceLang.GetDescription()}");
        var target = LangConverter(req.TargetLang) ?? throw new Exception($"该服务不支持{req.TargetLang.GetDescription()}");
        var content = req.Text;

        UriBuilder uriBuilder = new(Url);

        if (uriBuilder.Path == "/")
            uriBuilder.Path = "/chat/completions";

        // 选择模型
        var a_model = Model.Trim();
        a_model = string.IsNullOrEmpty(a_model) ? "deepseek-chat" : a_model;

        // 替换Prompt关键字
        var a_messages =
            (UserDefinePrompts.FirstOrDefault(x => x.Enabled)?.Prompts ?? throw new Exception("请先完善Propmpt配置")).Clone();
        a_messages.ToList().ForEach(item =>
            item.Content = item.Content.Replace("$source", source).Replace("$target", target)
                .Replace("$content", content));

        // 温度限定
        var a_temperature = Math.Clamp(Temperature, 0, 1);

        // 构建请求数据
        var reqData = new
        {
            model = a_model,
            messages = a_messages,
            temperature = a_temperature,
            stream = true
        };

        var jsonData = JsonConvert.SerializeObject(reqData);

        
        try
        {
            var sb = new StringBuilder();
            bool isThink = false;

            var resp = HttpUtil.PostAsync(uriBuilder.Uri, jsonData, AppKey, token);
            await foreach (var msg in resp)
            {
                if (string.IsNullOrEmpty(msg?.Trim()) || msg.StartsWith("event"))
                    continue;

                var preprocessString = msg.Replace("data:", "").Trim();

                // 结束标记
                if (preprocessString.Equals("[DONE]"))
                    continue;

                try
                {
                    // 解析JSON数据
                    var parsedData = JsonConvert.DeserializeObject<JObject>(preprocessString);

                    if (parsedData is null)
                        continue;

                    // 提取content的值
                    var contentValue = parsedData["choices"]?.FirstOrDefault()?["delta"]?["content"]?.ToString();

                    if (string.IsNullOrEmpty(contentValue))
                        continue;

                    /***********************************************************************
                     * 推理模型思考内容
                     * 1. content字段内：Groq（推理后带有换行）
                     * 2. reasoning_content字段内：DeepSeek、硅基流动（推理后带有换行）、第三方服务商
                     ************************************************************************/

                    #region 针对content内容中含有推理内容的优化

                    if (contentValue == "<think>")
                        isThink = true;
                    if (contentValue == "</think>")
                    {
                        isThink = false;
                        // 跳过当前内容
                        continue;
                    }

                    if (isThink)
                        continue;

                    #endregion

                    #region 针对推理过后带有换行的情况进行优化

                    // 优化推理模型思考结束后的\n\n符号
                    if (string.IsNullOrWhiteSpace(sb.ToString()) && string.IsNullOrWhiteSpace(contentValue))
                        continue;

                    sb.Append(contentValue);

                    #endregion

                    yield return contentValue;
                }
                catch
                {
                    // Ignore
                    // * 适配OpenRouter等第三方服务流数据中包含与OpenAI官方API中不同的数据
                    // * 如 ": OPENROUTER PROCESSING"
                }
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (HttpRequestException ex) when (ex.StatusCode == null)
        {
            var msg = "请检查服务是否可以正常访问: " + Name + " (" + Url + ").";
            throw new HttpRequestException(msg);
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException is { } innEx)
            {
                var innMsg = JsonConvert.DeserializeObject<JObject>(innEx.Message);
                msg += $