﻿using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using STranslate.Helper;
using STranslate.Log;
using STranslate.Model;
using STranslate.Util;
using System.ComponentModel;
using System.Net.Http;
using System.Text;
using System.Runtime.CompilerServices;

namespace STranslate.ViewModels.Preference.Translator;

public partial class TranslatorOpenRouter : TranslatorLLMBase, ITranslatorLLM
{
    #region Constructor

    public TranslatorOpenRouter()
        : this(Guid.NewGuid(), "https://openrouter.ai", "OpenRouter")
    {
    }

    public TranslatorOpenRouter(
        Guid guid,
        string url,
        string name = "",
        IconType icon = IconType.OpenRouter,
        string appID = "",
        string appKey = "",
        bool isEnabled = true,
        ServiceType type = ServiceType.OpenRouterService
    )
    {
        Identify = guid;
        Url = url;
        Name = name;
        Icon = icon;
        AppID = appID;
        AppKey = appKey;
        IsEnabled = isEnabled;
        Type = type;
    }

    #endregion Constructor

    #region Properties

    [JsonIgnore] private string _model = "openai/gpt-4o";
    public override string Model
    {
        get => _model;
        set => SetProperty(ref _model, value);
    }

    [JsonIgnore]
    private BindingList<string> _models =
    [
        "openai/gpt-4o"
    ];
    public override BindingList<string> Models
    {
        get => _models;
        set => SetProperty(ref _models, value);
    }

    #endregion

    #region Translator Test

    [property: JsonIgnore]
    [RelayCommand(IncludeCancelCommand = true)]
    private async Task TestAsync(CancellationToken token)
    {
        var result = "";
        var isCancel = false;
        try
        {
            IsTesting = true;
            var reqModel = new RequestModel("你好", LangEnum.zh_cn, LangEnum.en);
            await foreach (var item in TranslateAsync(reqModel, token))
            {
                result += item;
            }
        }
        catch (OperationCanceledException)
        {
            isCancel = true;
        }
        catch (Exception)
        {
            result = AppLanguageManager.GetString("Toast.VerifyFailed");
        }
        finally
        {
            IsTesting = false;
            if (!isCancel)
                ToastHelper.Show(result, WindowType.Preference);
        }
    }

    #endregion Translator Test

    #region Interface Implementation

    public new async IAsyncEnumerable<string> TranslateAsync(object request, [EnumeratorCancellation] CancellationToken token)
    {
        if (string.IsNullOrEmpty(Url))
            throw new Exception("请先完善配置");

        if (request is not RequestModel req)
            throw new Exception($"请求数据出错: {request}");

        //检查语种
        var source = LangConverter(req.SourceLang) ?? throw new Exception($"该服务不支持{req.SourceLang.GetDescription()}");
        var target = LangConverter(req.TargetLang) ?? throw new Exception($"该服务不支持{req.TargetLang.GetDescription()}");
        var content = req.Text;

        UriBuilder uriBuilder = new(Url);

        // 如果路径不是有效的API路径结尾，使用默认路径 https://openrouter.ai/docs/quickstart
        if (uriBuilder.Path == "/")
            uriBuilder.Path = "/api/v1/chat/completions";

        // 选择模型
        var a_model = Model.Trim();
        a_model = string.IsNullOrEmpty(a_model) ? "openai/gpt-4o" : a_model;

        // 替换Prompt关键字
        var a_messages =
            (UserDefinePrompts.FirstOrDefault(x => x.Enabled)?.Prompts ?? throw new Exception("请先完善Propmpt配置")).Clone();
        a_messages.ToList().ForEach(item =>
            item.Content = item.Content.Replace("$source", source).Replace("$target", target)
                .Replace("$content", content));

        // 温度限定
        var a_temperature = Math.Clamp(Temperature, 0, 2);
        
        // 构建请求数据
        var reqData = new
        {
            model = a_model,
            messages = a_messages,
            temperature = a_temperature,
            stream = true
        };

        var header = new Dictionary<string, string>
        {
            { "Authorization", $