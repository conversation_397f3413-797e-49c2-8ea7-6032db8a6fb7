using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace STranslate.Model
{
    public interface ITranslatorLLM : ITranslator
    {
        /// <summary>
        /// LLM
        /// </summary>
        /// <param name="prompts"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        Task<TranslationResult> TranslateAsync(IEnumerable<Prompt> prompts, CancellationToken token);
    }
}