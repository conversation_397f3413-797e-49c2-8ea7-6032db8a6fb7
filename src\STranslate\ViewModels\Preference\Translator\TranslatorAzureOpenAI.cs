﻿using System.ComponentModel;
using System.Net.Http;
using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using STranslate.Helper;
using STranslate.Log;
using STranslate.Model;
using STranslate.Util;
using System.Text;
using System.Runtime.CompilerServices;

namespace STranslate.ViewModels.Preference.Translator;

public partial class TranslatorAzureOpenAI : TranslatorLLMBase, ITranslatorLLM
{
    #region Constructor

    public TranslatorAzureOpenAI()
        : this(Guid.NewGuid(), "https://docs-test-001.openai.azure.com", "Azure OpenAI")
    {
    }

    public TranslatorAzureOpenAI (
        Guid guid,
        string url,
        string name = "",
        IconType icon = IconType.Azure,
        string appID = "",
        string appKey = "",
        bool isEnabled = true,
        ServiceType type = ServiceType.AzureOpenAIService
    )
    {
        Identify = guid;
        Url = url;
        Name = name;
        Icon = icon;
        AppID = appID;
        AppKey = appKey;
        IsEnabled = isEnabled;
        Type = type;
    }

    #endregion Constructor

    #region Properties

    [JsonIgnore]
    private BindingList<string> _models =
    [
        "gpt4",
        "gpt35",
    ];
    public override BindingList<string> Models
    {
        get => _models;
        set => SetProperty(ref _models, value);
    }

    #endregion

    #region Translator Test

    [property: JsonIgnore]
    [RelayCommand(IncludeCancelCommand = true)]
    private async Task TestAsync(CancellationToken token)
    {
        var result = "";
        var isCancel = false;
        try
        {
            IsTesting = true;
            var reqModel = new RequestModel("你好", LangEnum.zh_cn, LangEnum.en);
            await foreach (var item in TranslateAsync(reqModel, token))
            {
                result += item;
            }
        }
        catch (OperationCanceledException)
        {
            isCancel = true;
        }
        catch (Exception)
        {
            result = AppLanguageManager.GetString("Toast.VerifyFailed");
        }
        finally
        {
            IsTesting = false;
            if (!isCancel)
                ToastHelper.Show(result, WindowType.Preference);
        }
    }

    #endregion Translator Test

    #region Interface Implementation

    public new async IAsyncEnumerable<string> TranslateAsync(object request, [EnumeratorCancellation] CancellationToken token)
    {
        if (string.IsNullOrEmpty(Url) /* || string.IsNullOrEmpty(AppKey)*/)
            throw new Exception("请先完善配置");

        if (request is not RequestModel req)
            throw new Exception($"请求数据出错: {request}");

        //检查语种
        var source = LangConverter(req.SourceLang) ?? throw new Exception($"该服务不支持{req.SourceLang.GetDescription()}");
        var target = LangConverter(req.TargetLang) ?? throw new Exception($"该服务不支持{req.TargetLang.GetDescription()}");
        var content = req.Text;

        UriBuilder uriBuilder = new(Url);

        // 选择模型
        var a_model = Model.Trim();
        a_model = string.IsNullOrEmpty(a_model) ? "gpt35" : a_model;

        var path = $"/openai/deployments/{a_model}/chat/completions";
        if (uriBuilder.Path == "/")
            uriBuilder.Path = path;

        if (string.IsNullOrEmpty(uriBuilder.Query))
        {
            uriBuilder.Query = "?api-version=2024-02-01";
        }

        // 替换Prompt关键字
        var a_messages =
            (UserDefinePrompts.FirstOrDefault(x => x.Enabled)?.Prompts ?? throw new Exception("请先完善Propmpt配置")).Clone();
        a_messages.ToList().ForEach(item =>
            item.Content = item.Content.Replace("$source", source).Replace("$target", target)
                .Replace("$content", content));

        // 温度限定
        var a_temperature = Math.Clamp(Temperature, 0, 2);
        
        // 构建请求数据
        var reqData = new
        {
            messages = a_messages,
            temperature = a_temperature,
            stream = true
        };

        var header = new Dictionary<string, string>
        {
            { "api-key", AppKey }
        };
        var jsonData = JsonConvert.SerializeObject(reqData);

        try
        {
            var resp = HttpUtil.PostAsync(
                uriBuilder.Uri,
                header,
                jsonData,
                token
            );

            await foreach (var msg in resp)
            {
                if (string.IsNullOrEmpty(msg?.Trim()))
                    continue;

                var preprocessString = msg.Replace("data:", "").Trim();

                // 结束标记
                if (preprocessString.Equals("[DONE]"))
                    continue;

                // 解析JSON数据
                var parsedData = JsonConvert.DeserializeObject<JObject>(preprocessString);

                if (parsedData is null)
                    continue;

                // 提取content的值
                var contentValue = parsedData["choices"]?.FirstOrDefault()?["delta"]?["content"]?.ToString();

                if (string.IsNullOrEmpty(contentValue))
                    continue;

                yield return contentValue;
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (HttpRequestException ex) when (ex.StatusCode == null)
        {
            var msg = $"请检查服务是否可以正常访问: {Name} ({Url}).";
            throw new HttpRequestException(msg);
        }
        catch (HttpRequestException)
        {
            throw;
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException is { } innEx)
            {
                var innMsg = JsonConvert.DeserializeObject<JObject>(innEx.Message);
                msg += $